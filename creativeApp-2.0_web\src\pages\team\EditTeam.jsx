import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { alertMessage } from "../../common/coreui";
import Select from "react-select";

const API_URL = process.env.REACT_APP_BASE_API_URL;

const isTokenValid = () => {
  const token = localStorage.getItem("token");
  return token !== null;
};

const EditTeam = ({ dataItemsId, isVisible, setVisible }) => {
  const navigate = useNavigate();
  const [users, setUsers] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [team, setTeam] = useState(null);
  const [teamName, setTeamName] = useState("");
  const [icon, setIcon] = useState(null);
  const [existingIcon, setExistingIcon] = useState("");
  const [logo, setLogo] = useState(null);
  const [existingLogo, setExistingLogo] = useState("");
  const [poc, setPoc] = useState("");
  const [manager, setManager] = useState("");
  const [teamLead, setTeamLead] = useState("");
  const [workday, setWorkday] = useState([]);
  const [launch, setLaunch] = useState("");
  const [departmentId, setDepartmentId] = useState("");
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [loggedInUser, setLoggedInUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // React Select states
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [selectedPoc, setSelectedPoc] = useState(null);
  const [selectedManager, setSelectedManager] = useState(null);
  const [selectedTeamLead, setSelectedTeamLead] = useState(null);

  // Create options for React Select dropdowns
  const departmentOptions = departments.map(dept => ({
    value: dept.id,
    label: dept.name
  }));

  // Filter based on Responsibility Level (resource_types) instead of roles
  const pocOptions = users
    .filter(user => {
      const hasValidResponsibility = user.resource_types?.some(rt => ['Team Lead', 'Manager', 'HOD'].includes(rt));
      const hasValidName = user.fullName && user.fullName.trim() !== '';
      return hasValidResponsibility && hasValidName;
    })
    .map(user => ({
      value: user.fullName,
      label: user.fullName
    }));

  const managerOptions = users
    .filter(user => {
      const hasValidResponsibility = user.resource_types?.some(rt => ['Manager', 'HOD'].includes(rt));
      const hasValidName = user.fullName && user.fullName.trim() !== '';
      return hasValidResponsibility && hasValidName;
    })
    .map(user => ({
      value: user.fullName,
      label: user.fullName
    }));

  const teamLeadOptions = users
    .filter(user => {
      const hasValidResponsibility = user.resource_types?.some(rt => ['Team Lead', 'Manager', 'HOD'].includes(rt));
      const hasValidName = user.fullName && user.fullName.trim() !== '';
      return hasValidResponsibility && hasValidName;
    })
    .map(user => ({
      value: user.fullName,
      label: user.fullName
    }));

  // Debug logs for all options
  console.log('EditTeam - Total users:', users.length);
  console.log('EditTeam - Manager options:', managerOptions.length);
  console.log('EditTeam - POC options:', pocOptions.length);
  console.log('EditTeam - Team Lead options:', teamLeadOptions.length);


  // Days of the week for multi-select
  const daysOfWeek = [
    { value: 'Monday', label: 'Monday' },
    { value: 'Tuesday', label: 'Tuesday' },
    { value: 'Wednesday', label: 'Wednesday' },
    { value: 'Thursday', label: 'Thursday' },
    { value: 'Friday', label: 'Friday' },
    { value: 'Saturday', label: 'Saturday' },
    { value: 'Sunday', label: 'Sunday' }
  ];

  // Handle workday selection
  const handleWorkdayChange = (selectedOptions) => {
    const selectedValues = selectedOptions ? selectedOptions.map(option => option.value) : [];
    setWorkday(selectedValues);
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!isTokenValid()) {
        setError("No authentication token found.");
        setLoading(false);
        return;
      }

      const token = localStorage.getItem("token");

      try {
        // Fetch Users
        const usersResponse = await fetch(`${API_URL}/users`, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });

        if (!usersResponse.ok) {
          throw new Error("Failed to fetch users");
        }

        const usersData = await usersResponse.json();
        console.log('EditTeam Users data:', usersData); // Debug log
        setUsers(
          usersData.map((user) => ({
            id: user.id,
            fullName: `${(user.fname || "").trim()} ${(
              user.lname || ""
            ).trim()}`.trim(),
            fname: user.fname,
            lname: user.lname,
            roles: Array.isArray(user.roles)
              ? user.roles.map((r) => (r.name || "").trim())
              : [],
            resource_types: Array.isArray(user.resource_types)
              ? user.resource_types.map((rt) => (rt.name || "").trim())
              : [],
          }))
        );

        // Fetch Departments
        const departmentsResponse = await fetch(`${API_URL}/departments`, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });

        if (!departmentsResponse.ok) {
          throw new Error("Failed to fetch departments");
        }

        const departmentsData = await departmentsResponse.json();
        setDepartments(departmentsData.departments);

        // Fetch Team Details if editing an existing team
        if (dataItemsId) {
          const teamResponse = await fetch(`${API_URL}/teams/${dataItemsId}`, {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          });

          if (!teamResponse.ok) {
            throw new Error("Failed to fetch team details");
          }

          const teamData = await teamResponse.json();

          // Set team data to state
          setTeam(teamData.team); // Set the team object

          // Set values for form fields based on teamData
          setTeamName(teamData.team.name || ""); // Set default value for team name
          setIcon(teamData.team.icon || null); // Set default value for icon
          setLogo(teamData.team.logo || null); // Set default value for logo
          setPoc(teamData.team.poc || ""); // Set default value for POC
          setManager(teamData.team.manager || ""); // Set default value for manager
          setTeamLead(teamData.team.team_lead || ""); // Set default value for team lead
          // Handle workday - it comes as JSON string from backend
          let workdayArray = [];
          if (teamData.team.workday) {
            try {
              workdayArray = typeof teamData.team.workday === 'string'
                ? JSON.parse(teamData.team.workday)
                : teamData.team.workday;
            } catch (e) {
              console.error('Error parsing workday:', e);
              workdayArray = [];
            }
          }
          setWorkday(Array.isArray(workdayArray) ? workdayArray : []);
          setLaunch(teamData.team.launch || ""); // Set default value for launch
          setExistingIcon(teamData.team.icon || "");
          setExistingLogo(teamData.team.logo || "");

          // Set departmentId by accessing the first department's ID if available
          const departmentId =
            teamData.team.departments && teamData.team.departments.length > 0
              ? teamData.team.departments[0].id
              : "";
          setDepartmentId(departmentId); // Set the department ID

          // Set React Select default values after data is loaded
          setTimeout(() => {
            if (departmentId) {
              const deptOption = departmentsData.departments.find(d => d.id === departmentId);
              if (deptOption) {
                setSelectedDepartment({ value: deptOption.id, label: deptOption.name });
              }
            }

            if (teamData.team.poc) {
              setSelectedPoc({ value: teamData.team.poc, label: teamData.team.poc });
            }

            if (teamData.team.manager) {
              setSelectedManager({ value: teamData.team.manager, label: teamData.team.manager });
            }

            if (teamData.team.team_lead) {
              setSelectedTeamLead({ value: teamData.team.team_lead, label: teamData.team.team_lead });
            }
          }, 100); // Small delay to ensure options are rendered
        }
      } catch (error) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [dataItemsId]);

  // Fetch logged-in user data (user_id)
  useEffect(() => {
    const userId = localStorage.getItem("user_id");
    if (userId) {
      setLoggedInUser(userId);
    }
  }, []);

  const handleSubmit = async (event) => {
    event.preventDefault();
    setError(""); // Clear any previous error

    // Get user_id from localStorage for 'updated_by'
    const updatedBy = loggedInUser;

    if (!updatedBy) {
      setError("User is not logged in.");
      return;
    }

    try {
      const token = localStorage.getItem("token");
      if (!token) {
        setError("Authentication token is missing.");
        return;
      }

      // Fetch the logged-in user's data for full name
      const loggedUserResponse = await fetch(`${API_URL}/logged-users`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!loggedUserResponse.ok) {
        throw new Error("Failed to fetch logged-in user data");
      }

      const loggedUserData = await loggedUserResponse.json();
      const fullName = `${loggedUserData.fname} ${loggedUserData.lname}`; // Full name of the logged-in user
      console.log("Logged-in User Full Name:", fullName); // Log fullName for debugging

      // Prepare the data object (without file data)
      let processedWorkday;

      // Handle workday processing - ensure it's a valid JSON string
      if (Array.isArray(workday)) {
        processedWorkday = JSON.stringify(workday);
      } else if (typeof workday === 'string') {
        try {
          // If it's already a JSON string, validate it
          const parsed = JSON.parse(workday);
          processedWorkday = Array.isArray(parsed) ? JSON.stringify(parsed) : JSON.stringify([]);
        } catch (e) {
          // If parsing fails, treat as single value
          processedWorkday = JSON.stringify([workday]);
        }
      } else {
        processedWorkday = JSON.stringify([]);
      }

      console.log('EditTeam - Workday processing:', {
        original: workday,
        processed: processedWorkday,
        type: typeof workday
      });

      const requestData = {
        name: teamName.trim(),
        department_id: parseInt(selectedDepartment?.value || departmentId),
        launch: launch,
        workday: processedWorkday,
        poc: selectedPoc?.value || poc,
        manager: selectedManager?.value || manager,
        team_lead: selectedTeamLead?.value || teamLead,
        updated_by: updatedBy,
      };

      // Convert icon and logo files to Base64 if they are provided
      if (icon && icon instanceof File) {
        // Check if it's a valid File
        const iconBase64 = await convertToBase64(icon);
        requestData.icon = iconBase64;
      } else {
        console.log("No valid icon file selected.");
      }

      if (logo && logo instanceof File) {
        // Check if it's a valid File
        const logoBase64 = await convertToBase64(logo);
        requestData.logo = logoBase64;
      } else {
        console.log("No valid logo file selected.");
      }

      // Log the final request data before submission
      console.log("Request data before submission:", requestData);

      // Make the PUT request to update the team with JSON data
      const response = await fetch(`${API_URL}/teams/${dataItemsId}`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData), // Send data as JSON
      });

      if (!response.ok) {
        throw new Error("Failed to update team: " + response.statusText);
      }

      await response.json();
      alertMessage("success");

      // Optionally, close the modal after success
      setTimeout(() => {
        setVisible(false);
        setSuccessMessage("");
      }, 2000);

      navigate("/settings");
    } catch (error) {
      alertMessage("error");
    }
  };

  // Helper function to convert a file to Base64
  const convertToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      if (file instanceof File) {
        // Check if the parameter is a valid File object
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result); // resolve with the Base64 string
        reader.onerror = reject;
        reader.readAsDataURL(file); // Convert file to Base64
      } else {
        reject("The provided object is not a valid File.");
      }
    });
  };

  const handleClose = () => {
    setVisible(false);
    navigate("/settings");
  };



  
  return (
    <>
      {isVisible && (
        <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
          <div className="bg-white rounded-lg shadow-md w-full max-w-3xl relative">
            <div className="flex justify-between items-center mb-4 bg-gray-100">
              <h4 className="text-xl text-left font-medium text-gray-800 px-6 py-4">
                Edit Team
              </h4>
              <button
                className="text-3xl text-gray-500 hover:text-gray-800"
                onClick={handleClose}
              >
                &times;
              </button>
            </div>
            <form
              onSubmit={handleSubmit}
              className="text-left p-2 overflow-y-auto max-h-[90vh] scrollbar-vertical px-6"
            >
              <div className="flex flex-wrap items-start justify-between">
                {/* Department */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label
                    htmlFor="department"
                    className="block text-sm font-medium text-gray-700 pb-4"
                  >
                    Department
                  </label>
                  <Select
                    options={departmentOptions}
                    value={selectedDepartment}
                    onChange={(option) => {
                      setSelectedDepartment(option);
                      setDepartmentId(option?.value || '');
                    }}
                    placeholder="Select Department"
                    className="w-full"
                    isSearchable
                  />
                </div>

                {/* Team Name */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label
                    htmlFor="teamName"
                    className="block text-sm font-medium text-gray-700 pb-4"
                  >
                    Team Name
                  </label>
                  <input
                    type="text"
                    id="teamName"
                    value={teamName}
                    onChange={(e) => setTeamName(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>

                {/* POC */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label
                    htmlFor="poc"
                    className="block text-sm font-medium text-gray-700 pb-4"
                  >
                    POC
                  </label>
                  <Select
                    options={pocOptions}
                    value={selectedPoc}
                    onChange={(option) => {
                      setSelectedPoc(option);
                      setPoc(option?.value || '');
                    }}
                    placeholder="Select POC"
                    className="w-full"
                    isSearchable
                    noOptionsMessage={() => "No options available"}
                  />
                </div>

                {/* Manager */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label
                    htmlFor="manager"
                    className="block text-sm font-medium text-gray-700 pb-4"
                  >
                    Manager
                  </label>
                  <Select
                    options={managerOptions}
                    value={selectedManager}
                    onChange={(option) => {
                      setSelectedManager(option);
                      setManager(option?.value || '');
                    }}
                    placeholder="Select Manager"
                    className="w-full"
                    isSearchable
                    noOptionsMessage={() => "No managers found"}
                  />
                </div>

                {/* Team Lead */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label
                    htmlFor="teamLead"
                    className="block text-sm font-medium text-gray-700 pb-4"
                  >
                    Team Lead
                  </label>
                  {/* <select
                    id="teamLead"
                    value={teamLead}
                    onChange={(e) => setTeamLead(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    <option value="">Select Team Lead</option>
                    {users
                      .filter((u) =>
                        u.roles?.some((r) =>
                          [
                            "team-lead",
                            "manager",
                            "hod",
                            "admin",
                            "super-admin",
                          ].includes(r)
                        )
                      )
                      .map((u) => (
                        <option key={u.id} value={u.fullName}>
                          {u.fullName}
                        </option>
                      ))}
                  </select> */}
                   <Select
      options={teamLeadOptions}
      value={selectedTeamLead}
      onChange={(option) => {
        setSelectedTeamLead(option);
        setTeamLead(option?.value || '');
      }}
      placeholder="Select Team Lead"
      className="w-full"
      isSearchable
    />
                </div>

                {/* Workday */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label className="block text-sm font-medium text-gray-700 pb-4">
                    Work Days *
                  </label>
                  <Select
                    isMulti
                    options={daysOfWeek}
                    value={workday.map(day => ({ value: day, label: day }))}
                    onChange={handleWorkdayChange}
                    placeholder="Select Work Days"
                    className="w-full"
                    isDisabled={loading}
                    noOptionsMessage={() => "No options available"}
                  />
                  {workday.length > 0 && (
                    <p className="text-xs text-gray-500 mt-2">
                      Selected: {workday.join(", ")}
                    </p>
                  )}
                </div>

                {/* Launch */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label
                    htmlFor="launch"
                    className="block text-sm font-medium text-gray-700 pb-4"
                  >
                    Launch Date
                  </label>
                  <input
                    type="date"
                    id="launch"
                    value={launch}
                    onChange={(e) => setLaunch(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </div>
                <div className="mb-4 w-full sm:w-1/2 px-4 opacity-0"></div>

                {/* Icon Preview */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label
                    htmlFor="icon"
                    className="block text-sm font-medium text-gray-700 pb-4"
                  >
                    Icon
                  </label>
                  {icon && icon instanceof File ? (
                    <>
                      <img
                        src={URL.createObjectURL(icon)}
                        alt="Icon Preview"
                        className="w-20 h-20 mb-2"
                      />
                      <input
                        type="file"
                        id="icon"
                        onChange={(e) => setIcon(e.target.files[0])}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </>
                  ) : (
                    <>
                      {(existingIcon || icon) && (
                        <div className="w-20 h-20 overflow-hidden bg-gray-200 p-2 rounded-lg">
                          <img
                            // Prefer stored path when string
                            src={
                              typeof icon === "string" && icon
                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${icon}`
                                : existingIcon
                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingIcon}`
                                : ""
                            }
                            alt="Icon Preview"
                            className="w-auto h-auto object-cover"
                          />
                        </div>
                      )}
                      <input
                        type="file"
                        id="icon"
                        onChange={(e) => setIcon(e.target.files[0])}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </>
                  )}
                </div>

                {/* Logo Preview */}
                <div className="mb-4 w-full sm:w-1/2 px-4">
                  <label
                    htmlFor="logo"
                    className="block text-sm font-medium text-gray-700 pb-4"
                  >
                    Logo
                  </label>
                  {logo && logo instanceof File ? (
                    <>
                      <img
                        src={URL.createObjectURL(logo)}
                        alt="Logo Preview"
                        className="w-20 h-20 mb-2"
                      />
                      <input
                        type="file"
                        id="logo"
                        onChange={(e) => setLogo(e.target.files[0])}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </>
                  ) : (
                    <>
                      {(existingLogo || logo) && (
                        <div className="w-20 h-20 overflow-hidden bg-gray-200 p-2 rounded-lg">
                          <img
                            // Prefer stored path when string
                            src={
                              typeof logo === "string" && logo
                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${logo}`
                                : existingLogo
                                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingLogo}`
                                : ""
                            }
                            alt="Logo Preview"
                            className="w-auto h-auto object-cover"
                          />
                        </div>
                      )}
                      <input
                        type="file"
                        id="logo"
                        onChange={(e) => setLogo(e.target.files[0])}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </>
                  )}
                </div>
              </div>
              <div className="text-left pt-6">
                <button
                  type="submit"
                  className="w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4"
                >
                  <span class="material-symbols-rounded text-white text-xl font-regular">
                    add_circle
                  </span>
                  {loading ? "Updating..." : "Update Team"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
};

export default EditTeam;
